# Blender插件翻译器 - 快速开始指南

## 🚀 5分钟上手教程

### 第一步：安装插件

1. 下载 `plugin_translator.py` 文件
2. 打开Blender
3. 菜单：`编辑` → `首选项` → `插件`
4. 点击 `安装...` 按钮
5. 选择下载的 `plugin_translator.py` 文件
6. 在插件列表中勾选 "插件翻译器 (Plugin Translator)"

### 第二步：找到翻译面板

1. 在3D视口右侧，找到侧边栏（按 `N` 键显示/隐藏）
2. 点击 "翻译" 标签页
3. 你会看到插件翻译器的界面

### 第三步：扫描插件

1. 在 "扫描设置" 区域：
   - 如果要翻译特定插件，在 "目标插件" 输入插件名称
   - 如果要扫描所有插件，留空即可
2. 点击 "扫描插件" 按钮
3. 等待扫描完成，会显示找到多少个可翻译文本

### 第四步：手动翻译

1. 在翻译列表中点击任意项目
2. 在下方的编辑区域：
   - 查看英文原文
   - 在"中文翻译"框中输入对应的中文
   - 可以启用/禁用此翻译
   - 查看插件名称和上下文信息

### 第五步：应用翻译

1. 点击 "应用翻译" 按钮
2. 翻译立即生效！
3. 如果不满意，点击 "清除翻译" 恢复原文

### 第六步：保存翻译

1. 点击 "保存翻译" 按钮
2. 选择保存位置和文件名
3. 翻译会保存为JSON文件，可以分享给其他人

## 💡 实用技巧

### 翻译特定插件
- 在 "目标插件" 输入框中输入插件的部分名称
- 例如输入 "extra" 可以扫描所有包含 "extra" 的插件

### 高效翻译
- 先翻译最常用的界面元素（如面板标题、按钮名称）
- 再处理描述性文本和工具提示

### 分享翻译
- 保存翻译文件后，可以发送给朋友
- 朋友使用 "加载翻译" 就能使用你的翻译

### 管理多个翻译
- 为不同的插件创建不同的翻译文件
- 根据需要加载对应的翻译文件

## 🔧 常见问题

**Q: 翻译没有生效？**
A: 确保点击了 "应用翻译" 按钮，某些情况下可能需要重启Blender。

**Q: 扫描不到插件？**
A: 确保插件已启用，检查插件名称是否正确。

**Q: 翻译显示不完整？**
A: 某些插件可能使用特殊的文本处理方式，不是所有文本都能被翻译。

**Q: 如何恢复原文？**
A: 点击 "清除翻译" 按钮即可恢复所有原文。

**Q: 可以翻译成其他语言吗？**
A: 目前主要针对中文翻译，但你可以手动编辑翻译文本为任何语言。

## 📝 示例场景

### 场景1：翻译建模插件
1. 安装一个英文建模插件
2. 在目标插件输入插件名称
3. 扫描 → 手动翻译 → 应用翻译
4. 保存翻译文件备用

### 场景2：使用他人的翻译
1. 获得翻译JSON文件
2. 点击 "加载翻译"
3. 选择JSON文件
4. 点击 "应用翻译"

### 场景3：创建翻译包
1. 扫描所有插件（目标插件留空）
2. 逐一翻译所有文本
3. 保存为 "完整翻译包.json"
4. 分享给社区

## 🎯 高级用法

### 上下文翻译
- 同一个英文词在不同上下文可能有不同翻译
- 注意查看 "上下文" 字段，针对性翻译

### 批量操作
- 可以手动添加翻译项
- 适合添加插件扫描不到的文本

### 翻译文件编辑
- 翻译文件是标准JSON格式
- 可以用文本编辑器直接编辑
- 适合批量修改和高级编辑

---

**祝你使用愉快！如果遇到问题，请查看完整的README.md文档。**
