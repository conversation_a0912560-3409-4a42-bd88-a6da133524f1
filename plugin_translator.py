bl_info = {
    "name": "插件翻译器 (Plugin Translator)",
    "author": "Assistant",
    "version": (1, 0, 0),
    "blender": (2, 80, 0),
    "location": "View3D > 侧边栏 > 翻译",
    "description": "扫描已安装插件的英文文本，手动添加中文翻译",
    "category": "界面",
}

import bpy
import os
import json
import re
from bpy.types import Panel, Operator, PropertyGroup, UIList
from bpy.props import StringProperty, BoolProperty, CollectionProperty, IntProperty
from bpy.utils import register_class, unregister_class

# 全局翻译字典
translation_dict = {}

class TranslationItem(PropertyGroup):
    """翻译项属性组"""
    original_text: StringProperty(
        name="原文",
        description="英文原文",
        default=""
    )
    translated_text: StringProperty(
        name="译文",
        description="中文翻译",
        default=""
    )
    context: StringProperty(
        name="上下文",
        description="文本出现的上下文",
        default="*"
    )
    addon_name: StringProperty(
        name="插件名称",
        description="此翻译所属的插件名称",
        default=""
    )
    enabled: BoolProperty(
        name="启用",
        description="是否启用此翻译",
        default=True
    )

class TranslatorProperties(PropertyGroup):
    """翻译器属性"""
    translation_items: CollectionProperty(type=TranslationItem)
    active_translation_index: IntProperty(default=0)
    auto_translate: BoolProperty(
        name="自动翻译",
        description="启用后自动应用翻译",
        default=False
    )
    target_addon: StringProperty(
        name="目标插件",
        description="要翻译的插件名称（留空则扫描所有插件）",
        default=""
    )

class TRANSLATOR_UL_translation_list(UIList):
    """翻译列表UI"""
    def draw_item(self, context, layout, data, item, icon, active_data, active_propname):
        if self.layout_type in {'DEFAULT', 'COMPACT'}:
            row = layout.row(align=True)
            row.prop(item, "enabled", text="")
            row.label(text=item.addon_name, icon='ADDON')
            row.label(text=item.original_text[:30] + "..." if len(item.original_text) > 30 else item.original_text)
        elif self.layout_type in {'GRID'}:
            layout.alignment = 'CENTER'
            layout.label(text="", icon='ADDON')

class TRANSLATOR_OT_scan_addons(Operator):
    """扫描已安装的插件，查找可翻译的文本"""
    bl_idname = "translator.scan_addons"
    bl_label = "扫描插件"
    bl_description = "扫描已安装的插件，查找可翻译的英文文本"

    def execute(self, context):
        scene = context.scene
        translator_props = scene.translator_props

        # 清空现有的翻译项
        translator_props.translation_items.clear()

        # 获取所有已启用的插件
        found_texts = set()
        scanned_addons = 0

        for addon_name in bpy.context.preferences.addons.keys():
            if addon_name == __name__.split('.')[0]:  # 跳过自己
                continue

            # 如果指定了目标插件，只扫描该插件
            if translator_props.target_addon and translator_props.target_addon not in addon_name:
                continue

            try:
                # 扫描插件模块
                self.scan_addon(addon_name, found_texts, translator_props)
                scanned_addons += 1
            except Exception as e:
                print(f"扫描插件 {addon_name} 时出错: {e}")

        self.report({'INFO'}, f"扫描了 {scanned_addons} 个插件，找到 {len(translator_props.translation_items)} 个可翻译文本")
        return {'FINISHED'}

    def scan_addon(self, addon_name, found_texts, translator_props):
        """扫描单个插件"""
        # 获取插件模块
        addon_module = bpy.context.preferences.addons[addon_name].module
        if not addon_module:
            return

        # 扫描模块中的类
        for attr_name in dir(addon_module):
            try:
                attr = getattr(addon_module, attr_name)
                if self.is_blender_class(attr):
                    self.extract_texts_from_class(attr, addon_name, found_texts, translator_props)
            except:
                continue

    def is_blender_class(self, cls):
        """检查是否是Blender类"""
        return (hasattr(cls, '__bases__') and
                any(hasattr(base, 'bl_idname') or hasattr(base, 'bl_label')
                    for base in cls.__mro__ if hasattr(base, '__mro__')))

    def extract_texts_from_class(self, cls, addon_name, found_texts, translator_props):
        """从类中提取可翻译的文本"""
        # 常见的可翻译属性
        translatable_attrs = {
            'bl_label': 'Label',
            'bl_description': 'Description',
            'bl_category': 'Category',
            'bl_tooltip': 'Tooltip'
        }

        for attr, context in translatable_attrs.items():
            if hasattr(cls, attr):
                text = getattr(cls, attr)
                if isinstance(text, str) and text and self.is_english_text(text):
                    text_key = (addon_name, attr, text)
                    if text_key not in found_texts:
                        found_texts.add(text_key)

                        # 添加到翻译项列表
                        item = translator_props.translation_items.add()
                        item.original_text = text
                        item.addon_name = addon_name
                        item.context = context
                        item.translated_text = ""  # 等待用户填写

    def is_english_text(self, text):
        """检查文本是否为英文"""
        # 检查是否包含英文字母且不包含中文字符
        has_english = bool(re.search(r'[a-zA-Z]', text))
        has_chinese = bool(re.search(r'[\u4e00-\u9fff]', text))
        # 排除一些明显的标识符
        is_identifier = text.isupper() or '_' in text or '.' in text
        return has_english and not has_chinese and len(text) > 1 and not is_identifier

class TRANSLATOR_OT_add_translation(Operator):
    """手动添加翻译项"""
    bl_idname = "translator.add_translation"
    bl_label = "添加翻译"
    bl_description = "手动添加一个新的翻译项"

    def execute(self, context):
        scene = context.scene
        translator_props = scene.translator_props

        item = translator_props.translation_items.add()
        item.original_text = "New Text"
        item.translated_text = "新文本"
        item.addon_name = "手动添加"
        item.context = "Manual"

        translator_props.active_translation_index = len(translator_props.translation_items) - 1

        return {'FINISHED'}

class TRANSLATOR_OT_remove_translation(Operator):
    """删除翻译项"""
    bl_idname = "translator.remove_translation"
    bl_label = "删除翻译"
    bl_description = "删除选中的翻译项"

    def execute(self, context):
        scene = context.scene
        translator_props = scene.translator_props

        if (translator_props.active_translation_index >= 0 and
            translator_props.active_translation_index < len(translator_props.translation_items)):
            translator_props.translation_items.remove(translator_props.active_translation_index)

            # 调整活动索引
            if translator_props.active_translation_index >= len(translator_props.translation_items):
                translator_props.active_translation_index = len(translator_props.translation_items) - 1

        return {'FINISHED'}

class TRANSLATOR_OT_apply_translations(Operator):
    """应用翻译"""
    bl_idname = "translator.apply_translations"
    bl_label = "应用翻译"
    bl_description = "将翻译应用到Blender界面"

    def execute(self, context):
        scene = context.scene
        translator_props = scene.translator_props

        # 构建翻译字典
        global translation_dict
        translation_dict.clear()

        applied_count = 0
        for item in translator_props.translation_items:
            if item.enabled and item.translated_text.strip():
                key = ("*", item.original_text)  # 使用通用上下文
                translation_dict[key] = item.translated_text
                applied_count += 1

        # 注册翻译字典到Blender
        if translation_dict:
            langs = {'zh_CN': translation_dict}
            try:
                bpy.app.translations.unregister(__name__)
            except:
                pass
            bpy.app.translations.register(__name__, langs)

            self.report({'INFO'}, f"已应用 {applied_count} 个翻译")
        else:
            self.report({'WARNING'}, "没有可应用的翻译")

        return {'FINISHED'}

class TRANSLATOR_OT_clear_translations(Operator):
    """清除翻译"""
    bl_idname = "translator.clear_translations"
    bl_label = "清除翻译"
    bl_description = "清除所有已应用的翻译，恢复原文"

    def execute(self, context):
        try:
            bpy.app.translations.unregister(__name__)
            self.report({'INFO'}, "已清除所有翻译")
        except:
            self.report({'INFO'}, "没有需要清除的翻译")

        return {'FINISHED'}

class TRANSLATOR_OT_save_translations(Operator):
    """保存翻译到文件"""
    bl_idname = "translator.save_translations"
    bl_label = "保存翻译"
    bl_description = "将翻译保存到JSON文件"

    filepath: StringProperty(
        name="文件路径",
        description="保存翻译文件的路径",
        default="translations.json",
        subtype='FILE_PATH'
    )

    def execute(self, context):
        scene = context.scene
        translator_props = scene.translator_props

        # 构建保存数据
        save_data = {
            "version": "1.0",
            "translations": []
        }

        for item in translator_props.translation_items:
            save_data["translations"].append({
                'original_text': item.original_text,
                'translated_text': item.translated_text,
                'context': item.context,
                'addon_name': item.addon_name,
                'enabled': item.enabled
            })

        # 保存到文件
        try:
            with open(self.filepath, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2)
            self.report({'INFO'}, f"已保存 {len(save_data['translations'])} 个翻译到 {self.filepath}")
        except Exception as e:
            self.report({'ERROR'}, f"保存失败: {e}")

        return {'FINISHED'}

    def invoke(self, context, event):
        context.window_manager.fileselect_add(self)
        return {'RUNNING_MODAL'}

class TRANSLATOR_OT_load_translations(Operator):
    """从文件加载翻译"""
    bl_idname = "translator.load_translations"
    bl_label = "加载翻译"
    bl_description = "从JSON文件加载翻译"

    filepath: StringProperty(
        name="文件路径",
        description="加载翻译文件的路径",
        default="translations.json",
        subtype='FILE_PATH'
    )

    def execute(self, context):
        scene = context.scene
        translator_props = scene.translator_props

        try:
            with open(self.filepath, 'r', encoding='utf-8') as f:
                load_data = json.load(f)

            # 清空现有翻译
            translator_props.translation_items.clear()

            # 加载翻译项
            translations = load_data.get("translations", load_data)  # 兼容旧格式
            for data in translations:
                item = translator_props.translation_items.add()
                item.original_text = data.get('original_text', '')
                item.translated_text = data.get('translated_text', '')
                item.context = data.get('context', '*')
                item.addon_name = data.get('addon_name', '')
                item.enabled = data.get('enabled', True)

            self.report({'INFO'}, f"已从 {self.filepath} 加载 {len(translations)} 个翻译")

        except Exception as e:
            self.report({'ERROR'}, f"加载失败: {e}")

        return {'FINISHED'}

    def invoke(self, context, event):
        context.window_manager.fileselect_add(self)
        return {'RUNNING_MODAL'}

class TRANSLATOR_OT_clear_all_translations(Operator):
    """清空所有翻译项"""
    bl_idname = "translator.clear_all_translations"
    bl_label = "清空列表"
    bl_description = "清空所有翻译项"

    def execute(self, context):
        scene = context.scene
        translator_props = scene.translator_props

        translator_props.translation_items.clear()
        translator_props.active_translation_index = 0

        self.report({'INFO'}, "已清空所有翻译项")
        return {'FINISHED'}

    def invoke(self, context, event):
        return context.window_manager.invoke_confirm(self, event)

class TRANSLATOR_PT_main_panel(Panel):
    """主面板"""
    bl_label = "插件翻译器"
    bl_idname = "TRANSLATOR_PT_main_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "翻译"

    def draw(self, context):
        layout = self.layout
        scene = context.scene
        translator_props = scene.translator_props

        # 扫描设置
        box = layout.box()
        box.label(text="扫描设置", icon='SETTINGS')
        box.prop(translator_props, "target_addon", text="目标插件")
        box.operator("translator.scan_addons", icon='VIEWZOOM')

        # 翻译操作
        box = layout.box()
        box.label(text="翻译操作", icon='FILE_TEXT')
        row = box.row(align=True)
        row.operator("translator.apply_translations", icon='CHECKMARK', text="应用翻译")
        row.operator("translator.clear_translations", icon='X', text="清除翻译")

        # 列表管理
        box = layout.box()
        box.label(text="列表管理", icon='LINENUMBERS_ON')
        row = box.row(align=True)
        row.operator("translator.clear_all_translations", icon='TRASH', text="清空列表")

        # 文件操作
        box = layout.box()
        box.label(text="文件操作", icon='FILE_FOLDER')
        row = box.row(align=True)
        row.operator("translator.save_translations", icon='FILE_TICK')
        row.operator("translator.load_translations", icon='FILE_REFRESH')

        # 翻译列表
        box = layout.box()
        box.label(text=f"翻译列表 ({len(translator_props.translation_items)} 项)", icon='LINENUMBERS_ON')

        if translator_props.translation_items:
            row = box.row()
            row.template_list("TRANSLATOR_UL_translation_list", "",
                            translator_props, "translation_items",
                            translator_props, "active_translation_index")

            col = row.column(align=True)
            col.operator("translator.add_translation", icon='ADD', text="")
            col.operator("translator.remove_translation", icon='REMOVE', text="")

            # 显示选中项的详细信息
            if (translator_props.active_translation_index >= 0 and
                translator_props.active_translation_index < len(translator_props.translation_items)):

                item = translator_props.translation_items[translator_props.active_translation_index]

                detail_box = box.box()
                detail_box.label(text="编辑翻译", icon='GREASEPENCIL')
                detail_box.prop(item, "enabled", text="启用此翻译")
                detail_box.prop(item, "addon_name", text="插件名称")
                detail_box.prop(item, "context", text="上下文")
                detail_box.prop(item, "original_text", text="英文原文")
                detail_box.prop(item, "translated_text", text="中文翻译")

                # 添加说明
                if not item.translated_text.strip():
                    detail_box.label(text="💡 请在上方输入中文翻译", icon='INFO')
        else:
            box.label(text="没有找到可翻译的文本")
            box.label(text="请先点击'扫描插件'按钮")
            box.separator()
            box.label(text="使用步骤：")
            box.label(text="1. 在'目标插件'输入要翻译的插件名称")
            box.label(text="2. 点击'扫描插件'找出英文文本")
            box.label(text="3. 选择文本项，手动输入中文翻译")
            box.label(text="4. 点击'应用翻译'生效")

# 注册类列表
classes = [
    TranslationItem,
    TranslatorProperties,
    TRANSLATOR_UL_translation_list,
    TRANSLATOR_OT_scan_addons,
    TRANSLATOR_OT_add_translation,
    TRANSLATOR_OT_remove_translation,
    TRANSLATOR_OT_apply_translations,
    TRANSLATOR_OT_clear_translations,
    TRANSLATOR_OT_save_translations,
    TRANSLATOR_OT_load_translations,
    TRANSLATOR_OT_clear_all_translations,
    TRANSLATOR_PT_main_panel,
]

def register():
    """注册插件"""
    for cls in classes:
        register_class(cls)

    # 添加属性到场景
    bpy.types.Scene.translator_props = bpy.props.PointerProperty(type=TranslatorProperties)

    print("插件翻译器已启用")

def unregister():
    """注销插件"""
    # 清除翻译
    try:
        bpy.app.translations.unregister(__name__)
    except:
        pass

    # 删除属性
    if hasattr(bpy.types.Scene, 'translator_props'):
        del bpy.types.Scene.translator_props

    # 注销类
    for cls in reversed(classes):
        unregister_class(cls)

    print("插件翻译器已禁用")

if __name__ == "__main__":
    register()
