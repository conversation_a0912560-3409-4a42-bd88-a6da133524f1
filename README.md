# Blender 插件翻译器 (Plugin Translator)

一个专门用于扫描已安装插件的英文文本，让你手动添加中文翻译的工具。

## 功能特点

- 🔍 **智能扫描** - 自动检测已安装插件中的英文文本
- ✏️ **手动翻译** - 逐项手动添加中文翻译，确保准确性
- 💾 **保存分享** - 支持保存翻译到文件，方便分享和备份
- 🔄 **实时应用** - 一键应用翻译到Blender界面
- 🎯 **精确控制** - 可以选择性启用/禁用特定翻译
- 📋 **列表管理** - 清晰的列表界面，方便管理翻译项

## 安装方法

1. 下载 `plugin_translator.py` 文件
2. 打开Blender，进入 `编辑 > 首选项 > 插件`
3. 点击 `安装...` 按钮，选择下载的文件
4. 在插件列表中找到 "插件翻译器 (Plugin Translator)"，勾选启用
5. 在3D视口侧边栏会出现 "翻译" 标签页

## 使用方法

### 基本流程

1. **扫描插件**
   - 在 "扫描设置" 区域，输入要翻译的插件名称（留空则扫描所有插件）
   - 点击 "扫描插件" 按钮，系统会自动查找可翻译的英文文本

2. **手动翻译**
   - 在翻译列表中选择任意项目
   - 在下方的编辑区域手动输入中文翻译
   - 可以启用/禁用特定翻译项

3. **应用翻译**
   - 点击 "应用翻译" 按钮，翻译会立即生效
   - 点击 "清除翻译" 可以恢复原文

4. **保存分享**
   - 使用 "保存翻译" 将翻译保存为JSON文件
   - 使用 "加载翻译" 从文件加载之前保存的翻译

### 界面说明

#### 扫描设置
- **目标插件**: 指定要翻译的插件名称，留空则扫描所有插件
- **扫描插件**: 开始扫描插件中的可翻译文本

#### 翻译操作
- **应用翻译**: 将翻译应用到界面
- **清除翻译**: 清除所有翻译，恢复原文

#### 列表管理
- **清空列表**: 清空所有翻译项（需要确认）

#### 文件操作
- **保存翻译**: 将翻译保存到JSON文件
- **加载翻译**: 从JSON文件加载翻译

#### 翻译列表
- 显示所有找到的可翻译文本
- 可以选择、编辑、添加、删除翻译项
- 每个项目可以单独启用/禁用

## 支持的翻译内容

插件可以翻译以下类型的文本：
- 面板标题 (bl_label)
- 操作描述 (bl_description)
- 分类名称 (bl_category)
- 工具提示 (bl_tooltip)

## 翻译原理

插件通过扫描已安装插件的类属性来查找可翻译的文本，主要包括：
- 面板标题 (bl_label)
- 操作描述 (bl_description)
- 分类名称 (bl_category)
- 工具提示 (bl_tooltip)

然后使用Blender的内置国际化API来应用翻译。

## 文件格式

翻译文件使用JSON格式保存，结构如下：

```json
{
  "version": "1.0",
  "translations": [
    {
      "original_text": "Add Object",
      "translated_text": "添加物体",
      "context": "Label",
      "addon_name": "example_addon",
      "enabled": true
    }
  ]
}
```

## 注意事项

1. **重启Blender**: 某些翻译可能需要重启Blender才能完全生效
2. **插件兼容性**: 并非所有插件都支持翻译，取决于插件的编写方式
3. **备份翻译**: 建议定期保存翻译文件作为备份
4. **性能影响**: 大量翻译可能会轻微影响界面响应速度

## 故障排除

### 翻译不生效
- 确保点击了 "应用翻译" 按钮
- 检查翻译项是否已启用
- 尝试重启Blender

### 扫描不到文本
- 确保目标插件已启用
- 检查插件名称是否正确
- 某些插件可能使用特殊的文本处理方式

### 界面显示异常
- 点击 "清除翻译" 恢复原文
- 检查翻译文本是否过长
- 重新加载插件

## 技术原理

插件使用Blender的内置国际化API (`bpy.app.translations`) 来实现翻译功能。通过扫描已安装插件的类属性，提取可翻译的文本，然后构建翻译字典并注册到Blender系统中。

## 许可证

本插件基于MIT许可证发布，可以自由使用、修改和分发。

## 贡献

欢迎提交bug报告、功能建议或代码贡献！

---

**作者**: Assistant
**版本**: 1.0.0
**兼容性**: Blender 2.80+
