# Blender 插件翻译器 (Plugin Translator)

一个用于将其他英文Blender插件翻译成中文的工具插件。

## 功能特点

- 🔍 **自动扫描插件** - 自动检测已安装插件中的英文文本
- 🈯 **智能翻译** - 内置常用词汇词典，支持自动翻译
- ✏️ **手动编辑** - 可以手动添加和编辑翻译
- 💾 **保存加载** - 支持保存翻译到文件，方便分享和备份
- 🔄 **实时应用** - 一键应用翻译到Blender界面
- 🎯 **精确控制** - 可以选择性启用/禁用特定翻译

## 安装方法

1. 下载 `plugin_translator.py` 文件
2. 打开Blender，进入 `编辑 > 首选项 > 插件`
3. 点击 `安装...` 按钮，选择下载的文件
4. 在插件列表中找到 "插件翻译器 (Plugin Translator)"，勾选启用
5. 在3D视口侧边栏会出现 "翻译" 标签页

## 使用方法

### 基本流程

1. **扫描插件**
   - 在 "扫描设置" 区域，可以指定要翻译的插件名称（留空则扫描所有插件）
   - 点击 "扫描插件" 按钮，系统会自动查找可翻译的英文文本

2. **自动翻译**
   - 点击 "自动翻译" 按钮，系统会使用内置词典自动翻译常见词汇
   - 内置词典包含200+常用Blender术语

3. **手动编辑**
   - 在翻译列表中选择项目，在下方的编辑区域可以修改翻译
   - 可以手动添加新的翻译项

4. **应用翻译**
   - 点击 "应用翻译" 按钮，翻译会立即生效
   - 点击 "清除翻译" 可以恢复原文

5. **保存分享**
   - 使用 "保存翻译" 将翻译保存为JSON文件
   - 使用 "加载翻译" 从文件加载之前保存的翻译

### 界面说明

#### 扫描设置
- **目标插件**: 指定要翻译的插件名称，留空则扫描所有插件
- **扫描插件**: 开始扫描插件中的可翻译文本

#### 翻译操作
- **自动翻译**: 使用内置词典自动翻译
- **应用翻译**: 将翻译应用到界面
- **清除翻译**: 清除所有翻译，恢复原文

#### 文件操作
- **保存翻译**: 将翻译保存到JSON文件
- **加载翻译**: 从JSON文件加载翻译

#### 翻译列表
- 显示所有找到的可翻译文本
- 可以选择、编辑、添加、删除翻译项
- 每个项目可以单独启用/禁用

## 支持的翻译内容

插件可以翻译以下类型的文本：
- 面板标题 (bl_label)
- 操作描述 (bl_description)
- 分类名称 (bl_category)
- 工具提示 (bl_tooltip)

## 内置词典

插件内置了200+常用Blender术语的中英对照，包括：
- 基本操作：添加、删除、编辑、创建等
- 3D术语：网格、材质、纹理、渲染等
- 界面元素：面板、菜单、按钮、工具等
- 变换操作：缩放、旋转、移动、变换等
- 建模工具：挤出、倒角、细分、镜像等

## 文件格式

翻译文件使用JSON格式保存，结构如下：

```json
{
  "version": "1.0",
  "translations": [
    {
      "original_text": "Add Object",
      "translated_text": "添加物体",
      "context": "Label",
      "addon_name": "example_addon",
      "enabled": true
    }
  ]
}
```

## 注意事项

1. **重启Blender**: 某些翻译可能需要重启Blender才能完全生效
2. **插件兼容性**: 并非所有插件都支持翻译，取决于插件的编写方式
3. **备份翻译**: 建议定期保存翻译文件作为备份
4. **性能影响**: 大量翻译可能会轻微影响界面响应速度

## 故障排除

### 翻译不生效
- 确保点击了 "应用翻译" 按钮
- 检查翻译项是否已启用
- 尝试重启Blender

### 扫描不到文本
- 确保目标插件已启用
- 检查插件名称是否正确
- 某些插件可能使用特殊的文本处理方式

### 界面显示异常
- 点击 "清除翻译" 恢复原文
- 检查翻译文本是否过长
- 重新加载插件

## 技术原理

插件使用Blender的内置国际化API (`bpy.app.translations`) 来实现翻译功能。通过扫描已安装插件的类属性，提取可翻译的文本，然后构建翻译字典并注册到Blender系统中。

## 许可证

本插件基于MIT许可证发布，可以自由使用、修改和分发。

## 贡献

欢迎提交bug报告、功能建议或代码贡献！

---

**作者**: Assistant  
**版本**: 1.0.0  
**兼容性**: Blender 2.80+
